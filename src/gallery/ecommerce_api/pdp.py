from django.core.cache import cache
from django.utils.translation import get_language

from rest_framework import status
from rest_framework.generics import RetrieveAPIView
from rest_framework.response import Response

from custom.enums import (
    ShelfType,
    Sofa01Color,
)
from custom.metrics import metrics_client
from custom.shelf_states_interactor import add_jetty_state_to_redis
from ecommerce_api.mixins import EcommerceRefererBasedMixin
from events.domain_events.marketing_events import FurnitureViewEvent
from gallery.ecommerce_api.mixins import ColorOverrideViewMixin
from gallery.enums import (
    FurnitureCategory,
    ShelfStatusSource,
)
from gallery.models import (
    <PERSON>y,
    Sotty,
    <PERSON>y,
)
from gallery.serializers import (
    JettyPDPSerializer,
    WattyPDPSerializer,
)
from gallery.serializers.furniture.pdp import (
    SottyPDPSerializer,
    SottySingleModulePDPSerializer,
)
from gallery.services.prices_for_serializers import get_currency_rate
from promotions.utils import strikethrough_promo
from regions.mixins import RegionCalculationsObject
from regions.services.limitations import LimitationService
from reviews.queries import get_pdp_reviews
from reviews.serializers import GeneralReviewSerializer


class ProductPageBaseAPIView(
    ColorOverrideViewMixin,
    EcommerceRefererBasedMixin,
    RetrieveAPIView,
):
    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        self._trigger_analytics_events(instance)
        self._trigger_marketing_events(instance)

        reviews = self._get_reviews(instance.shelf_type, instance.furniture_category)
        response_data = {**serializer.data, **reviews}
        return Response(response_data, status=status.HTTP_200_OK)

    def get_serializer_context(self):
        return {
            'rco': RegionCalculationsObject(self.region),
            'region': self.region,
            'currency_rate': get_currency_rate(self.region),
            'striketrough_promo': strikethrough_promo(self.region),
            **super().get_serializer_context(),
        }

    @staticmethod
    def _get_reviews(
        shelf_type: ShelfType,
        furniture_category: FurnitureCategory,
    ) -> dict:
        language = get_language()
        cache_key = f'pdp_reviews_{language}_{shelf_type}_{furniture_category}'
        if reviews := cache.get(cache_key):
            return reviews

        queryset = get_pdp_reviews(shelf_type, furniture_category, language)
        serializer = GeneralReviewSerializer(queryset, context={'language': language})

        cache.set(cache_key, serializer.data, 60 * 60)

        return serializer.data

    def _trigger_analytics_events(self, instance):
        add_jetty_state_to_redis(
            user_id=self.request.user.id,
            jetty=instance,
            source=ShelfStatusSource.PRODUCT_VIEW,
            pagepath=self.request.META.get('HTTP_REFERER', 'missing referer'),
        )

        furniture_type = self.queryset.model.__name__.lower()
        agent_type = (
            'mobile'
            if self.request.user_agent.is_mobile or self.request.user_agent.is_tablet
            else 'web'
        )
        metrics_client().increment(
            'web.view.product',
            1,
            tags=[
                'agent_type:{}'.format(agent_type),
                'furniture_type:{}'.format(furniture_type),
            ],
        )

    def _trigger_marketing_events(self, instance):
        if self.request.user.is_authenticated:
            FurnitureViewEvent(
                user=self.request.user,
                last_viewed_furniture_id=instance.id,
                last_viewed_furniture_type=instance.furniture_type,
            )


class ProductPageJettyAPIView(ProductPageBaseAPIView):
    queryset = Jetty.objects.all().select_related('owner')
    serializer_class = JettyPDPSerializer


class ProductPageSottyAPIView(ProductPageBaseAPIView):
    queryset = Sotty.objects.all().select_related('owner')
    serializer_class = SottyPDPSerializer

    def get_object(self) -> Sotty:
        """Fallback to default material if UK and corduroy is selected."""
        obj = super().get_object()
        limitation_service = LimitationService(region=self.region)
        if (
            not limitation_service.is_corduroy_available
            and obj.fabric == Sotty.Fabric.CORDUROY
        ):
            obj.set_material(Sofa01Color.get_fallback(), with_save=False)
        return obj


class ProductPageSottySingleModuleAPIView(ProductPageSottyAPIView):
    queryset = Sotty.objects.filter(
        # filter only single modules
        preset=True
    ).select_related('owner')
    serializer_class = SottySingleModulePDPSerializer


class ProductPageWattyAPIView(ProductPageBaseAPIView):
    queryset = Watty.objects.all().select_related('owner')
    serializer_class = WattyPDPSerializer
